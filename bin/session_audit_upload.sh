#!/bin/bash

function usage {
  echo
  echo "usage: $0 [arguments]"
  echo
  echo "   Script per la creazione e l'upload via ftp del csv per l'audit degli accessi"
  echo
  echo "   arguments:"
  echo "     -d <days> .............. crea il csv relativo a <days> giorni fa, default $SESSION_AUDIT_DAYS_AGO"
  echo "     -n ..................... crea il csv ma senza caricarlo sul server ftp"
  echo "     -h ..................... mostra questa schermata d'aiuto"
  echo
}

FTPUPLOAD=1

while getopts hnd: flag; do
  case $flag in
    h)
      usage
      exit 0
      ;;
    n)
      FTPUPLOAD=0
      ;;
    d)
      re='^[0-9]+$'
      if ! [[ ${OPTARG} =~ $re ]] ; then
        echo "-d option ${OPTARG}: deve essere un numero >= 0" >&2
        usage
        exit 5
      fi
      SESSION_AUDIT_DAYS_AGO=${OPTARG}
      ;;
    ?)
      usage
      exit 4
      ;;
  esac
done

shift $(( OPTIND - 1 ));

SESSION_AUDIT_OUTPUT_FILE_NAME=${SESSION_AUDIT_FILE_PREFIX}_$(date -d "$SESSION_AUDIT_DAYS_AGO day ago" '+%Y%m%d').csv
SESSION_AUDIT_OUTPUT_FILE=$SESSION_AUDIT_OUTPUT_DIR/$SESSION_AUDIT_OUTPUT_FILE_NAME
D=$(date -d "$SESSION_AUDIT_DAYS_AGO day ago" '+%Y-%m-%d')  

echo "$(date '+%Y-%m-%d %H:%I:%S') INFO> $0 starting"
echo "$(date '+%Y-%m-%d %H:%I:%S') INFO> Creating file for day $D: $SESSION_AUDIT_OUTPUT_FILE"

grep -E  'GET /wpso/core/config .* 200 ' $SESSION_AUDIT_LOG_FILE | grep -Pv "($SESSION_AUDIT_EXCLUDE_USERS)" | awk -v var="$SESSION_AUDIT_APPLICATION_NAME" '{print $4 "-" $5 ".000000;" var ";" $3}' | sed 's/:/./g' | perl -e "while (\$l = <>) { print \$l if \$l =~ /$D/; }" >  $SESSION_AUDIT_OUTPUT_FILE

RET=$?
if [[ $RET != 0 ]]; then
    echo "$(date '+%Y-%m-%d %H:%I:%S') ERROR> Unable to create csv file"
    exit 1
fi

if [[ $FTPUPLOAD != 0 ]]; then
    echo "$(date '+%Y-%m-%d %H:%I:%S') INFO> Uploading file $SESSION_AUDIT_OUTPUT_FILE to server $SESSION_AUDIT_UPLOAD_SERVER in dir $SESSION_AUDIT_UPLOAD_DIR"
    FTPBATCHFILE=$(mktemp)
	(cat <<!
open $SESSION_AUDIT_UPLOAD_SERVER
user $SESSION_AUDIT_UPLOAD_USER $SESSION_AUDIT_UPLOAD_PASSWORD
lcd $SESSION_AUDIT_OUTPUT_DIR
cd $SESSION_AUDIT_UPLOAD_DIR
put $SESSION_AUDIT_OUTPUT_FILE_NAME
bye
!
) > $FTPBATCHFILE
    ftp -n < $FTPBATCHFILE
    RET=$?
    if [[ $RET != 0 ]]; then
        echo "$(date '+%Y-%m-%d %H:%I:%S') ERROR> Unable to upload file"
        exit 2
    fi
    rm $FTPBATCHFILE
fi

echo "$(date '+%Y-%m-%d %H:%I:%S') INFO> Done"
