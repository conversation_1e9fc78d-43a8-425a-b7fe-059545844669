#!/usr/bin/env perl

use strict;
use warnings;

use API::ART::REST;
use WPSOCORE;

use WebService::WSART::WPSOCOREWS;
use WebService::WSART::WPSOCOREWSPtes;
use WebService::WSART::WPSOCOREWSCabinets;
use WebService::WSART::WPSOCOREWSTestDataLoaders;
use WebService::WSART::WPSOCOREWSLoaders;
use WebService::WSART::WPSOCOREWSMaintenanceCorrective;
use WebService::WSART::WPSOCOREWSMaintenancePreemptive;
use WebService::WSART::WPSOCOREWSMaintenanceExtraordinary;
use WebService::WSART::WPSOCOREWSRoes;
use WebService::WSART::WPSOCOREWSTTs;
use WebService::WSART::WPSOCOREWSBot;
use WebService::WSART::WPSOCOREWSPFPs;
use WebService::WSART::WPSOCOREWSFtthAddresses;
use WebService::WSART::WPSOCOREWSWO001;
use WebService::WSART::WPSOCOREWSWO002;
use WebService::WSART::TailWS;

WPSOCORE::register_master_sessions(DEBUG => (defined $ENV{DANCER_ENVIRONMENT} && $ENV{DANCER_ENVIRONMENT} =~ /^production/ ? 0 : 1));

API::ART::REST->to_app;
