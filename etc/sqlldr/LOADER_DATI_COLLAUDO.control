OPTIONS (SKIP=1)
load data
APPEND
into table LOADER_TEST_DATA
fields terminated by '|'
TRAILING NULLCOLS
(
	OA,
	FOL,
	"AclRR",
	"Descrizione centrale",
	<PERSON><PERSON><PERSON><PERSON>,
	"TIP<PERSON>",	
	<PERSON>ND<PERSON>,
	NOTE,
	UIT,
	"AssTdR",
	AI,
	"DataCollaudo" DATE 'DD-MM-YYYY',
	"DataConnected" DATE 'YYYY-MM-DD',
	"OME AO",
	"<PERSON><PERSON><PERSON> AO",
	SS,
	SP,
	AVANZAMENTO,	
	FATTURAZIONE DATE 'YYYY-MM-DD',
	DATA_TEST DATE 'YYYY-MM-DD HH24:MI:SS',
	ESITO_COLLAUD,
	PRESENZA_OTDR,
	PRESENZA_FOTO,
	STATO_RCI,
	DATA_RICHIESTA_VUC DATE 'YYYY-MM-DD HH24:MI:SS',
	ID_STATO_VUC,
	DATA_VUC DATE 'YYYY-MM-DD HH24:MI:SS',
	DESC_IMPRESA,
	MODELL<PERSON>,
	"NETWORK_NGNeer",
	DESCR_ESITO_STATO,
	ATTEN_MIS,
	ATTEN_TEOR,
	DISTANZA,
	TRATTA_SEC,
	DISTR_ASSOCIATO,
	PROVINCIA,
	COMUNE,
	INDIRIZZO,
	EGON,
	"IdScheda ROE/PTE",
	"Data verifica ROE/PTE",
	"Esito verifica ROE/PTE",
	"Data regolarizzazione ROE/PTE",
	"IdScheda AO",
	"Data verifica AO",
	"Esito verifica AO",
	"Data regolarizzazione AO"
)
