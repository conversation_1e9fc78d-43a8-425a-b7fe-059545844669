<html>
	<head>
		<style>

			* {
				font-weight: normal;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 3mm;
			}

			table {
				border: 0.1mm solid #fff;
			}

			td {
				max-width: 0.02cm;
				min-width: 0.02cm;
				padding-top: 1.5mm;
				padding-bottom: 1.5mm;
				padding-left: 1.5mm;
				padding-right: 1.5mm;
				border-left: 0.1mm solid #fff;
				border-bottom: 0.1mm solid #fff;
			}

			tr td:first-child {
				border-left: none;
			}

			table tr:last-child td {
				border-bottom: none;
			}

			.strong-content {
				font-weight: bold;
			}

			.align-center {
				text-align: center;
			}

			.align-right {
				text-align: right;
			}

			.valign-top {
				vertical-align: top;
			}

			.border-top {
				border-top: 1px solid #000;
			}

			.border-bottom {
				border-bottom: 1px solid #000;
			}

			.border-left {
				border-left: 1px solid #000;
			}

			.border-right {
				border-right: 1px solid #000;
			}

			.border-all {
				border: 1px solid #000;
			}

			.tall-row {
				font-size: 1mm;
			}

			.low-color {
				color: #555;
			}

		</style>
	</head>
	<body>

		<!-- Header Title -->
		<table border="0" cellpadding="0" cellspacing="0" class="border-top border-bottom border-left border-right">
			<tbody>

				<tr>
					<td colspan="22" class="border-top border-bottom">
						<img src="file://[% EnvHash.ETC %]/templates/assets/logo_tim.jpg" height="40">
					</td>
					<td colspan="44" class="border-top border-left border-bottom align-center">
						<span class="strong-content">SCHEDA RILEVAZIONE</br>IRREGOLARITA' CON DISSERVIZIO</span>
					</td>
					<td colspan="22" class="border-top border-left border-bottom align-right">
						<img src="file://[% EnvHash.ETC %]/templates/assets/minilogo_sirti.png">
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22">&nbsp</td>
					<td colspan="44" class="border-all align-center" bgcolor= "yellow">
						<span class="strong-content">DATI DI INGAGGIO</span>
					</td>
					<td colspan="22">&nbsp</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="44" class="border-top">
						<span class="strong-content low-color">OA:</span>
						[% oa %]
					</td>
					<td colspan="44" class="border-top border-left">
						<span class="strong-content low-color">FOL:</span>
						[% FOL %]
					</td>

				</tr>
				<tr>
					<td colspan="44" class="border-top">
						<span class="strong-content low-color">COMPETENZA TERRITORIALE SIRTI:</span>
						[% corrRegion %]
					</td>
					<td colspan="44" class="border-top border-left">
						<span class="strong-content low-color">RIF TIM:</span>
						[% customerContact %]
					</td>

				</tr>
				<tr>
					<td colspan="44" class="border-top border-left border-bottom">
						<span class="strong-content low-color">ID GUASTO:</span>
						[% activityId %]
					</td>
					<td colspan="44" class="border-top border-left border-bottom">
						<span class="strong-content low-color">DATA INGAGGIO GUASTO:</span>
						[% IF openedBy == 'Cliente' %]
							[% creationDate %]
						[% END %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">COLLEGAMENTO:</span>
						[% mntLink | replace('"', '') | replace('\[', '') | replace('\]', '') %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top border-bottom">
						<span class="strong-content low-color">CODICE IMPIANTO:</span>
						[% mntPlantCode %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top border-bottom">
						<span class="strong-content low-color">CENTRALE A:</span>
						[% customerCentralA %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top border-bottom">
						<span class="strong-content low-color">CENTRALE Z:</span>
						[% customerCentralZ %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22">&nbsp</td>
					<td colspan="44" class="border-all align-center" bgcolor= "yellow">
						<span class="strong-content">DATI DI INTERVENTO</span>
					</td>
					<td colspan="22">&nbsp</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Inizio intervento</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% IF openedBy == 'Cliente' %]
							[% IF takeOverDateDay %]
								[% takeOverDateDay %]
							[% ELSIF assignmentDateDay %]
								[% assignmentDateDay %]
							[% END %]
						[% ELSE %]
							[% openDateDay %]	
						[% END %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% IF openedBy == 'Cliente' %]
							[% IF takeOverDateHour %]
								[% takeOverDateHour %]
							[% ELSIF assignmentDateHour %]
								[% assignmentDateHour %]
							[% END %]
						[% ELSE %]
							[% openDateHour %]	
						[% END %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">ID DIRETTRICE:</span>
						[% externalDirectiveId %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">DIRETTRICE:</span>
						[% directive %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">REGIONE SEDE GUASTO:</span>
						[% corrRegionDefectSeat %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">SEDE GUASTO:</span>
						[% mntDefectSeat %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">GESTORE SEDE GUASTO:</span>
						[% corrRegionDefectManagement %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">LOCALITA' GUASTO:</span>
						[% mntDefectStreet %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">INDIRIZZO:</span>
						[% mntDefectKm %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">QUANTITA' CAVI RTN DANNEGGIATI:</span>
						[% mntRTNDamagedCablesQuantity %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">NUMERO FIBRE DISSERVITE RTN:</span>
						[% mntRTNDisserviceCablesQuantity %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">NUMERO FIBRE DISSERVITE ASPI IN CANONE:</span>
						[% mntASPIDisserviceCablesQty %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">QUANTITA' DI CAVI ASPI NON IN CANONE DANNEGGIATI:</span>
						[% mntASPIOthDisserviceCablesQ %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">CAUSA DANNEGGIAMENTO:</span>
						[% reason %]
					</td>
				</tr>
				<tr>
					<td colspan="44" class="border-top">
						<span class="strong-content low-color">INFRASTRUTTURA DANNEGGIATA:</span>
					</td>
					<td colspan="8" class="border-top">&nbsp;</td>
					<td colspan="8" class="border-top align-center strong-content">
						<input type="checkbox"
							[% IF mntDamagedInfrastructure %]checked[% END %]
						>
						SI
					</td>
					<td colspan="8" class="border-top">&nbsp;</td>
					<td colspan="8" class="border-top align-center strong-content">
						<input type="checkbox"
							[% IF !mntDamagedInfrastructure %]checked[% END %]
						>
						NO
					</td>
					<td colspan="16" class="border-top">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="44" class="border-top border-bottom">
						<span class="strong-content low-color">NECESSITA' SCAVO:</span>
					</td>
					<td colspan="8" class="border-top border-bottom">&nbsp;</td>
					<td colspan="8" class="border-top align-center strong-content border-bottom">
						<input type="checkbox"
							[% IF diggingNeeded %]checked[% END %]
						>
						SI
					</td>
					<td colspan="8" class="border-top border-bottom">&nbsp;</td>
					<td colspan="8" class="border-top align-center strong-content border-bottom">
						<input type="checkbox"
							[% IF !diggingNeeded %]checked[% END %]
						>
						NO
					</td>
					<td colspan="16" class="border-top border-bottom">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Inizio giunzione</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% junctionStartDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% junctionStartDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Fine giunzione</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% junctionEndDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% junctionEndDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Fine disservizio da supervisione TIM</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% clientFeedbackDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% clientFeedbackDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top">
						<span class="strong-content low-color">TIPO DI RIPARAZIONE:</span>
						[% mntOperationType %]
					</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Fine intervento</span>
					</td>
					<td colspan="66" class="border-top border">
						<span class="strong-content low-color">Data:</span>
						[% repairDataDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% repairDataDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22">&nbsp</td>
					<td colspan="44" class="border-all align-center" bgcolor= "yellow">
						<span class="strong-content">ANNULLAMENTO INTERVENTO</span>
					</td>
					<td colspan="22">&nbsp</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Annullamento</span>
					</td>
					<td colspan="66" class="border-top border">
						<span class="strong-content low-color">Data:</span>
						[% wrongCompetenceDateDay or defectNotFoundDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% wrongCompetenceDateHour or defectNotFoundDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top border-bottom">
						<span class="strong-content low-color">CAUSALE ANNULLAMENTO:</span>
						[% cancelReason %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22">&nbsp</td>
					<td colspan="44" class="border-all align-center" bgcolor= "yellow">
						<span class="strong-content">SOSPENSIONE INTERVENTO</span>
					</td>
					<td colspan="22">&nbsp</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Inizio Sospensione</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% repairSuspensionDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% repairSuspensionDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22" rowspan="2" class="border-top border-right border-bottom">
						<span class="strong-content low-color">Fine Sospensione</span>
					</td>
					<td colspan="66" class="border-top border border-bottom">
						<span class="strong-content low-color">Data:</span>
						[% repairRestartDateDay %]
					</td>
				</tr>
				<tr>
					<td colspan="66" class="border-top border-bottom">
						<span class="strong-content low-color">Ora:</span>
						[% repairRestartDateHour %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-top border-bottom">
						<span class="strong-content low-color">CAUSALE SOSPENSIONE:</span>
						[% repairSuspensionReason %]
					</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="22">&nbsp</td>
					<td colspan="44" class="border-all align-center" bgcolor= "yellow">
						<span class="strong-content">NOTE</span>
					</td>
					<td colspan="22">&nbsp</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88" class="border-all">
						<span class="strong-content">DESCRIZIONE BREVE EVENTO E INTERVENTO DI RIPARAZIONE:</span>
						[% mntRepairNotes %]
					</td>
				</tr>
				<tr>
					<td colspan="88" class="tall-row">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="8">&nbsp;</td>
					<td colspan="28" class="align-center strong-content">Data</td>
					<td colspan="16">&nbsp;</td>
					<td colspan="28" class="align-center strong-content">Firma Impresa</td>
					<td colspan="8">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="88">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="8">&nbsp;</td>
					<td colspan="28" class="align-center border-bottom">[% repairDataDateDay or wrongCompetenceDateDay or defectNotFoundDateDay or globalDate %]</td>
					<td colspan="16">&nbsp;</td>
					<td colspan="28" class="align-center border-bottom">[% companyManager %]</td>
					<td colspan="8">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
					<td colspan="1">&nbsp;</td>
				</tr>
			</tbody>
		</table>
		<!-- Header Title -->

	</body>
</html>
