<html>
	<head>
		<style>
	
			* {
				font-weight: normal;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 10pt;
			}

			.new-page-class {
				page-break-inside: avoid;
			 }			

			.title-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
			}
			
			.from-to-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}

			.label-A-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.label-B-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.th-box-header-class {
				vertical-align: top;
				width: 50%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}

			.tb-from-to-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.label-from-to-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 0px;
			}
			
			.value-from-to-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}						
			
			.details-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}
			
			
			.tb-details-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.th-box-left-details-header-class {
				vertical-align: top;
				width: 70%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.th-box-right-details-header-class {
				vertical-align: top;
				width: 30%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
		
			.label-details-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 5px;
			}
			
			.value-details-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}
			
			.multiselect-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
			
			.thead-multiselect-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: center;
			}
			
			.label-box-desc-sosp-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
			}
			
			.value-box-desc-sosp-class {
				text-align: center;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
			}
			
			.elenco-persone-accesso-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
				
			.title-elenco-persone-accesso-class {
				text-align: center;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-size: 12pt;
				text-decoration: underline;
				font-style: italic;
			}
			
			.th-box-elenco-persone-accesso-class {
				vertical-align: top;
				width: 100%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.tb-elenco-persone-accesso-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				padding-top: 5px;
				padding-bottom: 5px;
				}
				
			.thead-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: darkgray;
				font-weight: bold;
			}
			
			.value-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left !important;
				font-weight: normal;
			}
			
			.rappresentante-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 10px;
				padding-top: 10px;
				padding-bottom: 10px;
				text-align: left;
				font-weight: normal;
				}

			.scelta-lavoro-modello-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 5px;
				text-align: left;
				font-weight: normal;
				}
				
			.value-scelta-lavoro-modello-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				font-weight: bold;
			}
			
			.elenco-responsabili-attivita-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				padding-top: 10px;
				padding-bottom: 10px;
				}
				
			.title-elenco-responsabili-attivita-class {
				text-align: center;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-size: 12pt;
				font-style: italic;
			}
			

		</style>
	</head>
	<body>
		
		<!-- Header Title -->
		<table class="title-header-class new-page-class">
			<tbody>
				<tr>
					<th class="label-A-title-header-class">C.I.L - Comunicazione Inizio Lavori</th>
					<th class="label-B-title-header-class">n.prog. <span>[% progCIL %]</span></th>
				</tr>
			</tbody>
		</table>
		<!-- Header Title -->
		
		<!-- Header From/To -->
		<table class="from-to-header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-header-class">
						<!-- From -->
						<table id="from" name="from" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">Da:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% supplierDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- From - end -->						
					</th>
					<th class="th-box-header-class">
						<!-- To -->
						<table id="to" name="to" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">A:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% buyerDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- To - end -->
					</th>
				</tr>
			</tbody>
		</table>
		<!-- Header From/To -->
		
		<!-- Header details -->
		<table class="details-header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-left-details-header-class">
						<!-- Left -->
						<table id="left" name="left" class="tb-details-header-class">
							<tbody>
								<tr>
									<th class="label-details-header-class">C.L.U. di</th>
									<th class="value-details-header-class">[% ftcName %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Centrale</th>
									<th class="value-details-header-class">[% central %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Prev. n&deg;</th>
									<th class="value-details-header-class">[% propositionNumber %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Network</th>
									<th class="value-details-header-class">[% networkId %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">N.ro ODA</th>
									<th class="value-details-header-class">[% workOrderId %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Operazione</th>
									<th class="value-details-header-class">[% operation %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Cod. Progetto Esecutivo</th>
									<th class="value-details-header-class">[% execProg %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Lavoro costo zero</th>
									<th class="value-details-header-class">[% zeroCostActivity %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Data effettiva inizio/sospensione/ripresa lavori</th>
									<th class="value-details-header-class">[% startDate %]</th>
								</tr>
							</tbody>
						</table> <!-- Left - end -->
					</th>
					<th class="th-box-right-details-header-class">
						<!-- right -->
						<table id="right" name="right" class="tb-details-header-class">
							<tbody>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Tecnica</th>
									<th class="value-details-header-class">[% technology %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Del</th>
									<th class="value-details-header-class">[% propositionDate %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Del</th>
									<th class="value-details-header-class"></th>
								</tr>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Revisione&sup1;</th>
									<th class="value-details-header-class">[% execProgRev %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Del</th>
									<th class="value-details-header-class">[% zeroCostActivityDate %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Data presunta fine lavori</th>
									<th class="value-details-header-class">[% presumedEndDate %]</th>
								</tr>
							</tbody>
						</table> <!-- right - end -->
					</th>
				</tr>
			</tbody>
		</table>
		<!-- Header details -->
		
		<!-- multiselect 1 -->
		
		<table class="multiselect-class new-page-class">
			<thead>
				<tr>
					<th class="thead-multiselect-class">Impianti di commutazione</th>
					<th class="thead-multiselect-class">Impianti di trasmissione</th>
					<th class="thead-multiselect-class">Sistemi di Alimentazione e condiz.</th>
					<th class="thead-multiselect-class">Rete di distribuzione e cavi di giunzione</th>
					<th class="thead-multiselect-class">Reti speciali (Dec.1,DV)</th>
					<th class="thead-multiselect-class">Sistemi di gestione</th>
					<th class="thead-multiselect-class">Lavori per conto esercizio</th>
					<th class="thead-multiselect-class">Lavori vari</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					[% IF technicalArea == 'ImpiantiCommutazione' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'ImpiantiTrasmissione' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'SistemiAlimentazioneCondizionamento' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'ReteDistribuzioneCavi' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'RetiSpeciali' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'SistemiGestione' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'LavoriContoEsercizio' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF technicalArea == 'LavoriVari' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
			</tbody>
		</table>
		<!-- multiselect 1 -->
		
		
		<table class="multiselect-class new-page-class">
			<tbody>
				<tr>
					<th class="label-box-desc-sosp-class">Descrizione lavori da effettuare/motivo sospensione:</th>
					[% IF ACTION_TYPE  == 'SOSPENSIONE' %]
					<th class="value-box-desc-sosp-class">[% suspensionReason %]</th>
					[% ELSIF ( ACTION_TYPE  == 'INIZIO_LAVORI' || ACTION_TYPE  == 'VARIAZIONE_PERSONALE' || ACTION_TYPE  == 'RIPRESA' ) %]
						[% IF activityNote  != '' && IOCDate != '' && IOCAddress != '' %]
							<th class="value-box-desc-sosp-class">[% activityNote %] - [% IOCDate %] - [% IOCAddress %]</th>
						[% ELSIF activityNote  != '' && IOCAddress != '' %]
							<th class="value-box-desc-sosp-class">[% activityNote %] - [% IOCAddress %]</th>
						[% ELSIF activityNote  != '' %]
							<th class="value-box-desc-sosp-class">[% activityNote %]</th>
						[% ELSIF IOCDate != '' && IOCAddress != '' %]
							<th class="value-box-desc-sosp-class">[% IOCDate %] - [% IOCAddress %]</th>
						[% ELSIF IOCAddress != '' %]
							<th class="value-box-desc-sosp-class">[% IOCAddress %]</th>
						[% END %]
					[% ELSE %]
					<th class="value-box-desc-sosp-class">[% activityNote %]</th>
					[% END %]
				</tr>
			</tbody>
		</table>
		
		<!-- multiselect 2 -->
		
		<table class="multiselect-class new-page-class">
			<thead>
				<tr>
					<th class="thead-multiselect-class">Prima comunicazione</th>
					<th class="thead-multiselect-class">Variazione durata</th>
					<th class="thead-multiselect-class">Variazione personale</th>
					<th class="thead-multiselect-class">Sospensione lavori</th>
					<th class="thead-multiselect-class">Ripresa lavori interrotti</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					[% IF ACTION_TYPE  == 'INIZIO_LAVORI' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF ACTION_TYPE  == 'VARIAZIONE_DURATA' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF ACTION_TYPE  == 'VARIAZIONE_PERSONALE' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF ACTION_TYPE  == 'SOSPENSIONE' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF ACTION_TYPE  == 'RIPRESA' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
				</tr>
			</tbody>
		</table>
		<!-- multiselect 2 -->
		
		<!-- rappresentante -->
		<table class="rappresentante-class new-page-class">
			<tbody>
				<tr>
					<th>Nominativo del responsabile del Servizio Prevenzione e protezione aziendale dell'Impresa: [% safetyAccountableName %] [% safetyAccountableSurname %]</th>
				</tr>
				<tr>
					<th><span style="text-decoration: underline;">Nota:</span> il suddetto personale si impegna a firmare il registro delle visite ed a rispettare le "Norme di comportamento Telecom Italia
SpA sugli accessi", le "Norme sulla sicurezza del servizio telefonico", ad operare sempre nel rispetto della legislazione vigente, dei
contratti, Capitolati e norme Tecniche emesse da Telecom Italia SpA.</th>
				</tr>
			</tbody>
		</table>
		<!-- rappresentante -->
		
		<!-- scelta-lavoro-modello -->
		
		<table class="scelta-lavoro-modello-class new-page-class">
			<tbody>
				<tr>
					<th class="value-scelta-lavoro-modello-class"><input type="checkbox" > Barrare se il lavoro &egrave; in subappalto</th>
					<th class="value-scelta-lavoro-modello-class"><input type="checkbox" > Barrare se il modello vale come richiesta collaudo</th>
				</tr>
			</tbody>
		</table>
		
		<!-- scelta-lavoro-modello -->
		
		<!-- elenco responsabili attivita -->
		
		<table class="elenco-responsabili-attivita-class new-page-class">
			<thead>
				<tr>
					<th class="title-elenco-responsabili-attivita-class">RESPONSABILI ATTIVITA' DITTE FORNITRICI/SUBFORNITRICI</th>
				</tr>
				<tr>
					<th class="th-box-elenco-persone-accesso-class">
						<table class="tb-elenco-persone-accesso-class">
							<thead>
								<tr>
									<th class="thead-elenco-persone-accesso-class">Ditta Fornitrice</th>
									<th class="thead-elenco-persone-accesso-class">Nome</th>
									<th class="thead-elenco-persone-accesso-class">Cognome</th>
									<th class="thead-elenco-persone-accesso-class">Telefono</th>
									<th class="thead-elenco-persone-accesso-class">Fax</th>
									<th class="thead-elenco-persone-accesso-class">Data (gg/mm/aaaa)</th>
									<th class="thead-elenco-persone-accesso-class">Tipo Fornitore</th>
									<th class="thead-elenco-persone-accesso-class">Firma</th>
								</tr>
							</thead>
							<tbody>
								<!-- puntamento diretto -->
								[% IF currentUserObj.lastName != '' %]
								<tr>
									<th class="value-elenco-persone-accesso-class">SIRTI SOCIETA PER AZIONI</th>
									<th class="value-elenco-persone-accesso-class">[% currentUserObj.firstName %]</th>
									<th class="value-elenco-persone-accesso-class">[% currentUserObj.lastName %]</th>
									<th class="value-elenco-persone-accesso-class">[% currentUserObj.mobilePhone %]</th>
									<th class="value-elenco-persone-accesso-class">[% currentUserObj.mobilePhone %]</th>
									<th class="value-elenco-persone-accesso-class">[% globalDate %]</th>
									<th class="value-elenco-persone-accesso-class">Fornitore</th>
									<th class="value-elenco-persone-accesso-class">______________________</th>
								</tr>
								[% END %]
								<!-- puntamento diretto -->
								[% IF subDescription != '' %]
								<tr>
									<th class="value-elenco-persone-accesso-class">[% subDescription %]</th>
									<th class="value-elenco-persone-accesso-class">[% subAccountableName %]</th>
									<th class="value-elenco-persone-accesso-class">[% subAccountableSurname %]</th>
									<th class="value-elenco-persone-accesso-class">[% subPhone %]</th>
									<th class="value-elenco-persone-accesso-class">[% subFax %]</th>
									<th class="value-elenco-persone-accesso-class">[% subDate %]</th>
									<th class="value-elenco-persone-accesso-class">Subfornitore</th>
									<th class="value-elenco-persone-accesso-class">______________________</th>
								</tr>
								[% END %]
							</tbody>
						</table>
					</th>					
				</tr>
			</tbody>
		</table>
		
		<!-- elenco responsabili attivita -->
		
		<!-- legenda -->
		<table class="rappresentante-class new-page-class">
			<tbody>
				<tr>
					<th>&sup1; La revisione &egrave; quella del progetto esecutivo</th>
				</tr>
				<tr>
					<th>&sup2; Indicare eventuale Responsabile cantiere/Preposto (626/94)</th>
				</tr>
			</tbody>
		</table>
		<!-- legenda -->		
		
	</body>
</html>
