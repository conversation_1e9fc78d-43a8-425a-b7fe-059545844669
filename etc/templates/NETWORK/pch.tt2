<html>
	<head>
		<style>
	
			* {
				font-weight: normal;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 10pt;
			}
			
			.new-page-class {
				page-break-inside: avoid;
			 }
			 
			.header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
			}
			
			.from-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}

			.to-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.label-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 0px;
			}
			
			.label-5-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 5px;
			}
			
			.value-20-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 20px;
				word-break: break-all;
				white-space: normal;
			}
			
			.value-5-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}					
			
			.th-box-header-class {
				vertical-align: top;
				width: 50%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.box-top-prestazioni-class {
				padding-top: 30px;
				border: 0px;
				width: 100%;
			}
			
			.prestazioni-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: center;
				width: 100%;
				border-width: 1px;
				border-style: solid;
				border-color: black;
			}
			
			.label-prestazioni-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-size: 11pt;
				font-weight: bold;
				color: darkgray;
				vertical-align: top;
			  text-align: left;
			}
			
			.value-prestatizoni-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-weight: normal;
				word-break: break-all;
				white-space: normal;
				vertical-align: top;
				font-size: x-small;
			}
			
			.label-totali-class {
				font-weight: normal;
				vertical-align: top;
			  text-align: right;
				width: 70%;
			}			
			
			.value-totali-class {
				font-weight: normal;
				vertical-align: top;
			  text-align: right;
				width: 30%;
			}
			
			.label-bold-totali-class {
				font-size: 11pt;
				font-weight: bold;
				vertical-align: top;
			  text-align: right;
				width: 70%;
			}			
			
			.value-bold-totali-class {
				font-size: 11pt;
				font-weight: bold;
				vertical-align: top;
			  text-align: right;
				width: 30%;
			}
			
			.hr-totali-class {
				display: block;
				margin-top: 0.5em;
				margin-bottom: 0.5em;
				margin-left: auto;
				margin-right: auto;
				border-style: inset;
				border-width: 2px;
				color: darkgray;
			}
			
		</style>
	</head>
	<body>
		<!-- Header -->
		<table id="header" name="header" class="header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-header-class">
						<!-- From -->
						<table id="from" name="from" class="from-header-class">
							<tbody>
								<tr>
									<th colspan="2" class="label-header-class">Mittente</th>
								</tr>
								<tr>
									<th colspan="2" class="value-20-header-class">[% header.buyerDesc %]</th>
								</tr>
								<tr>
									<th colspan="2" class="label-header-class">Divisione</th>
								</tr>
								<tr>
									<th colspan="2" class="value-20-header-class">[% header.SAPDivision %] [% header.SAPDivisionDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Data</th>
									<th class="value-5-header-class">[% header.propositionDate %]</th>
								</tr>
								<tr>
									<th colspan="2" class="label-5-header-class">RIFERIMENTO PROGETTO</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Ordine di acquisto:</th>
									<th class="value-5-header-class">[% header.workOrderId %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Tipo intervento:</th>
									<th class="value-5-header-class">[% header.customerWBE %] ([% header.customerWBEDesc %])</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Pdb:</th>
									<th class="value-5-header-class">[% header.businessProject %] ([% header.businessProjectDesc %])</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Operazione:</th>
									<th class="value-5-header-class">[% header.operation %] ([% header.operationDesc %])</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Network:</th>
									<th class="value-5-header-class">[% header.networkId %] ([% header.networkDesc %])</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Stato:</th>
									<th class="value-5-header-class">[% header.networkStatus  %]</th>
								</tr>
							</tbody>
						</table> <!-- From - end -->
					</th>
					<th class="th-box-header-class">
						<!-- To -->
						<table id="to" name="to" class="to-header-class">
							<tbody>
								<tr>
									<th colspan="2" class="label-header-class">Destinatario</th>
								</tr>
								<tr>
									<th colspan="2" class="value-20-header-class">[% header.supplierDesc %]</th>
								</tr>
								<tr>
									<th colspan="2" class="label-header-class">Fornitore merce</th>
								</tr>
								<tr>
									<th colspan="2" class="value-20-header-class">[% header.goodsSupplierDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Data prevista inizio lav.:</th>
									<th class="value-5-header-class">[% header.forecastStartDate %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Data prevista fine lav.:</th>
									<th class="value-5-header-class">[% header.forecastEndDate %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Sett. Rich.:</th>
									<th class="value-5-header-class">[% header.requestorContext %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Sede Tecnica:</th>
									<th class="value-5-header-class">[% header.technicalSite %] [% header.technicalSiteDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Destinatario:</th>
									<th class="value-5-header-class">[% header.goodsRecipient %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Fatt. di Val.:</th>
									<th class="value-5-header-class">[% header.enhancementFactorDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Tipo Impianto:</th>
									<th class="value-5-header-class">[% header.centralId %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Area Lav./Tipologia:</th>
									<th class="value-5-header-class">[% header.customerWorkingArea %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Prog./real:</th>
									<th class="value-5-header-class">[% header.customerTechnicalAssistant %] [% header.customerTechnicalAssistantDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Tipo progetto:</th>
									<th class="value-5-header-class">[% header.customerProjectType %] [% header.customerProjectTypeDesc %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Riferimento ISO:</th>
									<th class="value-5-header-class">[% header.isoReference %]</th>
								</tr>
								<tr>
									<th class="label-5-header-class">Contratto:</th>
									<th class="value-5-header-class">[% header.propositionContractId %]</th>
								</tr>
							</tbody>
						</table> <!-- To - end -->
					</th>
				</tr>
			</tbody>
		</table> <!-- Header - end -->
		
		
		<!-- Prestazioni -->
		<div class="new-page-class">
			
			<table class="box-top-prestazioni-class">
				<tbody>
					<tr>
						<th class="label-header-class">Prestazioni</th>
					</tr>
				</tbody>
			</table>
	
			<table class="prestazioni-class">
				<thead>
					<tr>
						<th class="label-prestazioni-class" style="width: 15%;text-align: left">Codice Materiale</th>
						<th class="label-prestazioni-class" style="width: 35%;text-align: left">Descrizione</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Prezzo unitario</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Quantit&agrave;</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Unit&agrave; di prezzo</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Valore posizione</th>
						<th class="label-prestazioni-class" style="width: 5%;text-align: left">Valuta</th>
					</tr>
				</thead>
				<tbody>
					<!-- ciclo generazione materiali con itemType == 'Performance' -->
					[% FOREACH materiale IN details %]
						[% IF materiale.itemType == 'Performance' %]
					<tr>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.productNumber %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.productDesc %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.unitPrice %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.quantity %] [% materiale.unitOfMeasure %] </th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.itemUnitPrice %] [% materiale.unitOfMeasure %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.itemTotalAmount %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">EUR</th>
					</tr>
						[% END %]
					[% END %]
				</tbody>
			</table>
		</div>
		<!-- Prestazioni -->
		
		<!-- Materiali fornitore -->
		
		<div class="new-page-class">
		
			<table class="box-top-prestazioni-class">
				<tbody>
					<tr>
						<th class="label-header-class">Materiali fornitore</th>
					</tr>
				</tbody>
			</table>
	
			<table class="prestazioni-class">
				<thead>
					<tr>
						<th class="label-prestazioni-class" style="width: 15%;text-align: left">Codice Materiale</th>
						<th class="label-prestazioni-class" style="width: 35%;text-align: left">Descrizione</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Prezzo unitario</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Quantit&agrave;</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Unit&agrave; di prezzo</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Valore posizione</th>
						<th class="label-prestazioni-class" style="width: 5%;text-align: left">Valuta</th>
					</tr>
				</thead>
				<tbody>
					<!-- ciclo generazione materiali -->
					[% FOREACH materiale IN details %]
						[% IF materiale.itemType == 'SupplierMaterial' %]
					<tr>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.productNumber %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.productDesc %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.unitPrice %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.quantity %] [% materiale.unitOfMeasure %] </th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.itemUnitPrice %] [% materiale.unitOfMeasure %] </th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.itemTotalAmount %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">EUR</th>
					</tr>
						[% END %]
					[% END %]
				</tbody>
			</table>
		</div>	
		
		<!-- Materiali fornitore -->
		
		<!-- Materiali Telecom -->
		
		<div class="new-page-class">
		
			<table class="box-top-prestazioni-class">
				<tbody>
					<tr>
						<th class="label-header-class">Materiali Telecom</th>
					</tr>
				</tbody>
			</table>
	
			<table class="prestazioni-class">
				<thead>
					<tr>
						<th class="label-prestazioni-class" style="width: 20%;text-align: left">Codice Materiale</th>
						<th class="label-prestazioni-class" style="width: 20%;text-align: left">Codice magazzino</th>
						<th class="label-prestazioni-class" style="width: 40%;text-align: left">Descrizione Magazzino</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: right">Quantit&agrave;</th>
						<th class="label-prestazioni-class" style="width: 10%;text-align: left">Partita</th>
					</tr>
				</thead>
				<tbody>
					<!-- ciclo generazione materiali -->
					[% FOREACH materiale IN details %]
						[% IF materiale.itemType == 'BuyerMaterial' %]
					<tr>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.productNumber %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.customerWarehouse %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.customerWarehouseDesc %]</th>
						<th class="value-prestatizoni-class" style="text-align: right">[% materiale.quantity %]</th>
						<th class="value-prestatizoni-class" style="text-align: left">[% materiale.supplierWarehouse %]</th>
					</tr>
						[% END %]
					[% END %]
				</tbody>
			</table>
		
		</div>
		
		<!-- Materiali Telecom -->
		
		<!-- TOTALI -->
		
		<div class="new-page-class">

			<table class="box-top-prestazioni-class">
				<tbody>
					<tr>
						<th class="label-header-class">TOTALI</th>
					</tr>
				</tbody>
			</table>
					
			<table class="prestazioni-class">
				<tbody>
					<tr>
						<th class="label-totali-class">Tot. Prestazioni</th>
						<th class="value-totali-class">[% header.servicesTotalAmount %]</th>
					</tr>
					<tr>
						<th class="label-totali-class">Tot. Materiali Fornitori</th>
						<th class="value-totali-class">[% header.supplierGoodsTotalAmount %]</th>
					</tr>
					<tr>
						<th class="label-totali-class">Tot. MOS</th>
						<th class="value-totali-class">[% header.MOSTotalAmount %]</th>
					</tr>
					<tr>
						<th class="label-totali-class">Tot. Risorse</th>
						<th class="value-totali-class">[% header.resourcesTotalAmount %]</th>
					</tr>
					<tr>
						<th class="label-totali-class"></th>
						<th class="value-totali-class">
							<hr class="hr-totali-class">
						</th>
					</tr>
					<tr>
						<th class="label-bold-totali-class">TOTALE PREVENTIVO</th>
						<th class="value-bold-totali-class">[% header.totalAmount %]</th>
					</tr>
				</tbody>
			</table>
			
		</div>
		
		<!-- TOTALI -->
		
	</body>
</html>