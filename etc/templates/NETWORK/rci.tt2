<html>
	<head>
		<style>
	
			* {
				font-weight: normal;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 10pt;
			}
			
			.new-page-class {
				page-break-inside: avoid;
			 }			

			.title-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
			}
			
			.from-to-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}

			.label-A-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.label-B-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.th-box-header-class {
				vertical-align: top;
				width: 50%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}

			.tb-from-to-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.label-from-to-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 0px;
			}
			
			.value-from-to-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}						
			
			.details-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}
			
			.box-common-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 0px;
				padding-bottom: 0px;
				}
				
			.box-dati-collaudo-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 5px;
				padding-bottom: 5px;
				}

			
			.label-box-common-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 0px;
				padding-left: 0px;
				padding-top: 0px;
				padding-right: 0px;
			}
			
			.value-box-common-class {
				text-align: left;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 0px;
				padding-left: 0px;
				padding-top: 0px;
				padding-right: 0px;
			}
			
			.thead-box-common-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: darkgray;
				font-weight: bold;
			}
			
			.value-tbody-box-common-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				font-weight: normal;
			}
			
			.elenco-persone-accesso-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
				
			.title-elenco-persone-accesso-class {
				text-align: center;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-size: 12pt;
				text-decoration: underline;
				font-style: italic;
			}
			
			.th-box-elenco-persone-accesso-class {
				vertical-align: top;
				width: 100%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.tb-elenco-persone-accesso-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				padding-top: 5px;
				padding-bottom: 5px;
				}
				
			.thead-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: darkgray;
				font-weight: bold;
			}
			
			.value-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				font-weight: normal;
			}
			
			.legenda-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 30px;
				padding-top: 10px;
				padding-bottom: 10px;
				text-align: left;
				font-weight: normal;
				}
				
			.label-desc-intervento-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 0px;
				padding-left: 0px;
				padding-top: 0px;
				padding-right: 0px;
				width: 20%;
			}
				
			.value-desc-intervento-class {
				text-align: left;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 0px;
				padding-left: 0px;
				padding-top: 0px;
				padding-right: 0px;
				width: 80%;
			}

			.value-firma-class {
				text-align: center;
				vertical-align: top;
				font-weight: normal;
			}	

			.spazio-telecom-class {

				border-left-width: 0px;
				border-right-width: 0px;
				border-bottom-width: 0px;
				border-top-width: 3px;
				border-style: dashed;
				border-color: black;
				width: 100%;
				margin-top: 30px;
				padding-top: 10px;
				padding-bottom: 10px;
				text-align: left;
				font-weight: normal;
			}
			
			.thead-spazio-telecom-class {
				padding-bottom: 20px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: black;
				font-weight: bold;
				font-size: 12pt;
			}
			
			.tbody-label-spazio-telecom-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: black;
				font-weight: normal;
				width: 20%;
			}
			
			.tbody-value-spazio-telecom-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: black;
				font-weight: normal;
				width: 80%;
			}
			
		</style>
	</head>
	<body>
		
		<!-- Header Title -->
		<table class="title-header-class new-page-class">
			<tbody>
				<tr>
					<th class="label-A-title-header-class">R.C.I - Richiesta di Collaudo Impianto</th>
					<th class="label-B-title-header-class">n.prog. <span>[% progRCI %]</span></th>
				</tr>
			</tbody>
		</table>
		<!-- Header Title -->
		
		<!-- Header From/To -->
		<table class="from-to-header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-header-class">
						<!-- From -->
						<table id="from" name="from" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">Da:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% supplierDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- From - end -->						
					</th>
					<th class="th-box-header-class">
						<!-- To -->
						<table id="to" name="to" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">A:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% buyerDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- To - end -->
					</th>
				</tr>
			</tbody>
		</table>
		<!-- Header From/To -->
		
		<table class="box-common-class new-page-class">
			<tbody>
				<tr>
					<th class="label-box-common-class">Si richiede con la presente di collaudare in data <span class="value-box-common-class" >[% testDate %]</span> ora <span class="value-box-common-class" >[% testTime %]</span></th>
				</tr>
				<tr>
					<th class="value-box-common-class">Il collaudo dell' impianto sar&agrave; svolto da parte nostra prima dell'emissione del documento CUI.</th>
				</tr>
				<tr>
					<th class="label-box-common-class">Il nostro personale di riferimento per questa attivit&agrave; &egrave;
						<span class="value-box-common-class" >[% safetyAccountableName %] [% safetyAccountableSurname %]</span>
						Tel <span class="value-box-common-class" >[% tdta0021 %]</span>
						Fax <span class="value-box-common-class" >[% tdta0022 %]</span>
					</th>
				</tr>
			</tbody>
		</table>
		
		<!-- elenco persone accesso -->
		
		<table class="box-dati-collaudo-class new-page-class">
			<thead>
				<tr>
					<th class="thead-box-common-class">Centrale Localit&agrave; Sede Cliente</th>
					<th class="thead-box-common-class">Fornitore</th>
					<th class="thead-box-common-class">Network</th>
					<th class="thead-box-common-class">N.ro ODA</th>
					<th class="thead-box-common-class">Operazione</th>
					<th class="thead-box-common-class">Codice del Progetto Esecutivo</th>
					<th class="thead-box-common-class">Revisione del Progetto Esecutivo</th>
					<th class="thead-box-common-class" style="width: 10%">Data Fine Lavori Prevista</th>
					<th class="thead-box-common-class">Realizzazione T.P.S.&sup1;</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th class="value-tbody-box-common-class">[% central %]</th>
					<th class="value-tbody-box-common-class">[% supplierId %]</th>
					<th class="value-tbody-box-common-class">[% networkId %]</th>
					<th class="value-tbody-box-common-class">[% workOrderId %]</th>
					<th class="value-tbody-box-common-class">[% operation %]</th>
					<th class="value-tbody-box-common-class">[% execProg %]</th>
					<th class="value-tbody-box-common-class">[% execProgRev %]</th>
					<th class="value-tbody-box-common-class" style="width: 10%">[% endDate %]</th>
					<th class="value-tbody-box-common-class">[% shipmentType %]</th>
				</tr>
			</tbody>
		</table>

		
		<!-- elenco persone accesso -->
		
		
		<!-- elenco persone accesso -->
		
		<table class="elenco-persone-accesso-class new-page-class">
			<thead>
				<tr>
					<th class="title-elenco-persone-accesso-class">ELENCO DELLE PERSONE PER LE QUALI SI COMUNICA L'ACCESSO:</th>
				</tr>
				<tr>
					<th class="th-box-elenco-persone-accesso-class">
						<table class="tb-elenco-persone-accesso-class">
							<thead>
								<tr>
									<th class="thead-elenco-persone-accesso-class">Cognome</th>
									<th class="thead-elenco-persone-accesso-class">Nome</th>
									<th class="thead-elenco-persone-accesso-class">Qualifica</th>
									<th class="thead-elenco-persone-accesso-class">N&deg; badge di T.I.</th>
									<th class="thead-elenco-persone-accesso-class">N&deg; tess. di ricon.</th>
									<th class="thead-elenco-persone-accesso-class">Note&sup2;</th>
								</tr>
							</thead>
							<tbody>
								<!-- ciclo generazione materiali -->
								[% FOREACH persona IN people %]
								<tr>
									<th class="value-elenco-persone-accesso-class">[% persona.surname %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.name %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.businessRole %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.badge %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.companyCard %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.note %]</th>
								</tr>
								[% END %]
							</tbody>
						</table>
					</th>					
				</tr>
			</tbody>
		</table>
		
		<!-- elenco persone accesso -->		
		
		<table class="box-common-class new-page-class">
			<tbody>
				<tr>
					<th class="label-desc-intervento-class">Descrizione Intervento /Impianto Note: <span class="value-desc-intervento-class">[% activityNote %]</span></th>
				</tr>
			</tbody>
		</table>		
		

		
		<!-- rappresentante -->
		<table class="legenda-class new-page-class">
			<tbody>
				<tr>
					<th>Vogliate restituirci entro due giorni lavorativi dalla ricezione il documento compilato nelle parti di Vostra competenza.</th>
				</tr>
			</tbody>
		</table>
		
		<!-- rappresentante -->
		<table class="legenda-class new-page-class">
			<tbody>
				<tr>
					<th class="value-firma-class">Data: [% globalDate %]</th>
					<th class="value-firma-class">Firma (Leggibile): __________________________________</th>
				</tr>
			</tbody>
		</table>
		
		<!-- rappresentante -->
		
		<!-- legenda -->
		<table class="legenda-class new-page-class">
			<tbody>
				<tr>
					<th>&sup1; T.P.S.= Totale, Parziale, Saldo</th>
				</tr>
				<tr>
					<th>&sup2; Indicare eventuale Responsabile cantiere/Preposto (626/94)</th>
				</tr>
			</tbody>
		</table>
		<!-- legenda -->						
		
		<!-- sezione telecom -->
		
		<table class="spazio-telecom-class new-page-class" >
			<thead>
				<tr>
					<th colspan="2" class="thead-spazio-telecom-class">
						Spazio riservato per la risposta, a cura di Telecom Italia S.p.A., alla Richiesta di Collaudo Impianto.
					</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th class="tbody-label-spazio-telecom-class">
						Da:
					</th>
					<th class="tbody-value-spazio-telecom-class">_______________________________________________________________________________</th>
				</tr>
				<tr>
					<th class="tbody-label-spazio-telecom-class">
						A (Forniotre/Impresa):
					</th>
					<th class="tbody-value-spazio-telecom-class">_______________________________________________________________________________</th>
				</tr>
				<tr>
					<th class="tbody-label-spazio-telecom-class">
						p.c. :
					</th>
					<th class="tbody-value-spazio-telecom-class">_______________________________________________________________________________</th>
				</tr>
				<tr>
					<th colspan="2" class="tbody-label-spazio-telecom-class">Vi comunichiamo che:</th>
				</tr>
				<tr>
					<th colspan="2" class="tbody-value-spazio-telecom-class"><span><input type="checkbox" > Parteciperemo al collaudo</span><span style="padding-left: 70px;"><input type="checkbox" > Non parteciperemo al collaudo</span></th>
				</tr>
			</tbody>
		</table>
		
		<!-- rappresentante -->
		<table class="legenda-class new-page-class">
			<tbody>
				<tr>
					<th class="value-firma-class">Data: [% dataDoc %]</th>
					<th class="value-firma-class">Firma (Leggibile): __________________________________</th>
				</tr>
			</tbody>
		</table>
		
		
		<!-- sezione telecom -->
		
	</body>
</html>