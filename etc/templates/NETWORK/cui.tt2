<html>
	<head>
		<style>
	
			* {
				font-weight: normal;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 10pt;
			}
			
			.new-page-class {
				page-break-inside: avoid;
			 }				

			.title-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
			}
			
			.from-to-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}

			.label-A-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.label-B-title-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 50%;
				font-weight: bold;
			}
			
			.th-box-header-class {
				vertical-align: top;
				width: 50%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}

			.tb-from-to-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.label-from-to-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 0px;
			}
			
			.value-from-to-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}						
			
			.details-header-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
			}
			
			
			.tb-details-header-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				text-align: left;
				width: 100%;
			}
			
			.th-box-left-details-header-class {
				vertical-align: top;
				width: 70%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.th-box-right-details-header-class {
				vertical-align: top;
				width: 30%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
		
			.label-details-header-class {
				font-weight: bold;
				vertical-align: top;
				padding-bottom: 5px;
			}
			
			.value-details-header-class {
				font-weight: normal;
				vertical-align: top;
				padding-bottom: 5px;
				word-break: break-all;
				white-space: normal;
			}
			
			.tipo-consegna-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
			
			.thead-tipo-consegna-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: center;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.thead-title-tipo-consegna-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: middle;
			  text-align: center;
				border-width: 0px;
				border-style: solid;
				border-color: black;
				font-weight: bold
			}				

			.tipo-consegna-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
			
			
			.label-tipo-consegna-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;

			}
			
			.value-tipo-consegna-class {
				text-align: left;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				white-space: normal;
			}			
			
			
			.dic-responsabilita-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				}
			
			
			.label-dic-responsabilita-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;

			}
			
			.value-dic-responsabilita-class {
				text-align: left;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
			}
			

			.controlli-collaudi-class {
				width: 100%;
				margin-top: 15px;
				padding-top: 10px;
				padding-bottom: 10px;
				border-collapse: collapse;
				}
				
			.title-controlli-collaudi-class {
				text-align: center;
				width: 100%;
				font-style: italic;
				font-weight: bold;
				font-size: 12pt;
			}
			
			
			.doc-install-allegata-class {
				width: 100%;
				margin-top: 0px;
				padding-top: 10px;
				padding-bottom: 10px;
				border-collapse: collapse;
				}
				
			.title-doc-install-allegata-class {
				text-align: center;
				width: 100%;
				font-style: italic;
				font-weight: bold;
				font-size: 12pt;
			}
			
			.value-doc-install-allegata-class {
				text-align: left;
				border: 1px solid black;
				width: 50%;
			}
			
			.text-doc-install-allegata-class {
				font-weight: bold;
				padding-left: 15px;
			}
			
			.controlli-collaudi-class {
				width: 100%;
				margin-top: 15px;
				padding-top: 10px;
				padding-bottom: 10px;
				border-collapse: collapse;
				}
				
			.title-controlli-collaudi-class {
				text-align: center;
				width: 100%;
				font-style: italic;
				font-weight: bold;
				font-size: 12pt;
			}
			
		
			.title2-controlli-collaudi-class {
				text-align: left;
				width: 100%;
				font-style: normal;
				font-weight: bold;
				padding-left: 5px;
				border: 1px solid black;
			}

			.title3-controlli-collaudi-class {
				text-align: left;
				width: 100%;
				font-style: italic;
				font-weight: bold;
				padding-left: 5px;
				padding-top: 10px;
			}
			

			
			.value-top-left-controlli-collaudi-class {
				text-align: left;
				width: 48%;
				font-weight: bold;
				border-bottom:  1px solid black !important;
				border-right: none;
				border-left: 1px solid black !important;
				border-top: 1px solid black !important;
			}
			
			.value-top-center-controlli-collaudi-class {
				font-weight: bold;
				text-align: center;
				width: 4% !important;
				border-bottom:  none;
				border-right: none;
				border-left: none;
				border-top: 1px solid black !important;
			}
			
			.value-middle-center-controlli-collaudi-class {
				border-bottom:  none;
				border-right: none;
				border-left: none;
				border-top: none;
				font-weight: bold;
				text-align: center;
				width: 4% !important;
			}
			
			.value-bottom-center-controlli-collaudi-class {
				font-weight: bold;
				text-align: center;
				width: 4% !important;
				border-bottom:  1px solid black !important;
				border-right: none;
				border-left: none;
				border-top: none;
			}
			
			.value-top-right-controlli-collaudi-class {
				text-align: left;
				width: 48%;
				font-weight: bold;
				border-bottom:  1px solid black !important;
				border-right: 1px solid black !important;
				border-left: none;
				border-top: 1px solid black !important;
			}
			
			.text-controlli-collaudi-class {
				font-weight: bold;
				padding-left: 15px;
			}
			
			.rappresentante-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 10px;
				padding-top: 10px;
				padding-bottom: 10px;
				text-align: left;
				font-weight: normal;
				}
				
			.elenco-persone-accesso-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				padding-top: 10px;
				padding-bottom: 10px;
				}
				
			.title-elenco-persone-accesso-class {
				text-align: center;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				font-size: 12pt;
				text-decoration: underline;
				font-style: italic;
			}
			
			.th-box-elenco-persone-accesso-class {
				vertical-align: top;
				width: 100%;
				border-width: 0px;
				border-style: solid;
				border-color: black;
			}
			
			.tb-elenco-persone-accesso-class {
				border-width: 0px;
				border-style: solid;
				border-color: black;
				width: 100%;
				padding-top: 5px;
				padding-bottom: 5px;
				}
				
			.thead-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				color: darkgray;
				font-weight: bold;
			}
			
			.value-elenco-persone-accesso-class {
				padding-bottom: 5px;
				padding-left: 5px;
				padding-top: 5px;
				padding-right: 5px;
				vertical-align: top;
			  text-align: left;
				font-weight: normal;
			}
			
			.sup-value-class {
				font-size: 7pt;
				font-weight: bold;
			}
			
			
			.totale-cui-class {
				border-width: 1px;
				border-style: solid;
				border-color: black;
				width: 100%;
				margin-top: 20px;
				}
			
			
			.label-totale-cui-class {
				text-align: left;
				vertical-align: top;
				font-weight: bold;
				padding-bottom: 2px;
				padding-left: 5px;
				padding-top: 2px;
				padding-right: 5px;

			}
			
			.value-totale-cui-class {
				text-align: left;
				vertical-align: top;
				font-weight: normal;
				padding-bottom: 2px;
				padding-left: 5px;
				padding-top: 2px;
				padding-right: 5px;
			}			

		</style>
	</head>
	<body>
		
		<!-- Header Title -->
		<table class="title-header-class new-page-class">
			<tbody>
				<tr>
					<th class="label-A-title-header-class">C.U.I - Comunicazione Ultimazione Impianto</th>
					<th class="label-B-title-header-class">n.prog. <span>[% progCUI %]</span></th>
				</tr>
			</tbody>
		</table>
		<!-- Header Title -->
		
		<!-- Header From/To -->
		<table class="from-to-header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-header-class">
						<!-- From -->
						<table id="from" name="from" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">Da:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% supplierDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- From - end -->						
					</th>
					<th class="th-box-header-class">
						<!-- To -->
						<table id="to" name="to" class="tb-from-to-header-class">
							<tbody>
								<tr>
									<th class="label-from-to-header-class">A:</th>
								</tr>
								<tr>
									<th class="value-from-to-header-class">[% buyerDesc %]</th>
								</tr>
							</tbody>
						</table> <!-- To - end -->
					</th>
				</tr>
			</tbody>
		</table>
		<!-- Header From/To -->
		
		<!-- Header details -->
		<table class="details-header-class new-page-class">
			<tbody>
				<tr>
					<th class="th-box-left-details-header-class">
						<!-- Left -->
						<table id="left" name="left" class="tb-details-header-class">
							<tbody>
								<tr>
									<th class="label-details-header-class">C.L.U. di</th>
									<th class="value-details-header-class">[% ftcName %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Centrale</th>
									<th class="value-details-header-class">[% central %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Prev. n&deg;</th>
									<th class="value-details-header-class">[% propositionNumber %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Network</th>
									<th class="value-details-header-class">[% networkId %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">N.ro ODA</th>
									<th class="value-details-header-class">[% workOrderId %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Operazione</th>
									<th class="value-details-header-class">[% operation %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Cod. Progetto Esecutivo</th>
									<th class="value-details-header-class">[% execProg %]</th>
								</tr>
							</tbody>
						</table> <!-- Left - end -->
					</th>
					<th class="th-box-right-details-header-class">
						<!-- right -->
						<table id="right" name="right" class="tb-details-header-class">
							<tbody>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Tecnica</th>
									<th class="value-details-header-class">[% technology %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Del</th>
									<th class="value-details-header-class">[% propositionDate %]</th>
								</tr>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Del</th>
									<th class="value-details-header-class"></th>
								</tr>
								<tr>
									<th class="label-details-header-class">&nbsp;</th>
									<th class="value-details-header-class">&nbsp;</th>
								</tr>
								<tr>
									<th class="label-details-header-class">Revisione<sup class="sup-value-class">1</sup></th>
									<th class="value-details-header-class">[% execProgRev %]</th>
								</tr>
							</tbody>
						</table> <!-- right - end -->
					</th>
				</tr>
			</tbody>
		</table>
		<!-- Header details -->
		
		<!-- tipo-consegna -->
		
		<table class="tipo-consegna-class new-page-class">
			<tbody>
				<tr>
					<td rowspan="2"  class="thead-title-tipo-consegna-class">Tipo di consegna</td>
					<td class="thead-tipo-consegna-class">Totale</td>
					<td class="thead-tipo-consegna-class">Parziale</td>
					<td class="thead-tipo-consegna-class">A Saldo</td>
				</tr>
				<tr>
					[% IF shipmentType == 'Totale' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF shipmentType == 'Parziale' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
					[% IF shipmentType == 'Saldo' %]
					<th><input type="checkbox" checked></th>
					[% ELSE %]
					<th><input type="checkbox" ></th>
					[% END %]
				</tr>
			</tbody>
		</table>
		<!-- tipo-consegna -->
		
		
		<table class="tipo-consegna-class new-page-class">
			<tbody>
				<tr style="display: none">
					<th class="label-tipo-consegna-class">Importo :</th>
					<th colspan="3" class="value-tipo-consegna-class">[% servicesTotalAmount %]</th>
				</tr>
				<tr>
					<th class="label-tipo-consegna-class">Descrizione lavori da effettuare :</th>
					<th colspan="3" class="value-tipo-consegna-class">[% activityNote %]</th>
				</tr>
				<tr>
					<th class="label-tipo-consegna-class">Segnalazioni particolari :</th>
					<th colspan="3" class="value-tipo-consegna-class">[% particularNotes %]</th>
				</tr>
				<tr>
					<th rowspan="2" class="label-tipo-consegna-class" style="vertical-align: middle">Il collaudo &egrave; stato eseguito da noi in data :</th>
					<th rowspan="2" class="value-tipo-consegna-class" style="vertical-align: middle">[% testDate %]</th>
					<th class="value-tipo-consegna-class" style="text-align: center">In presenza di personale TI</th>
					<th class="value-tipo-consegna-class" style="text-align: center">In assenza di personale TI</th>
				</tr>
				<tr>
					<th class="value-tipo-consegna-class" style="text-align: center">
						[% IF partyAtTest == 'Presente' %]
						<input type="radio" checked>
						[% ELSE %]
						<input type="radio">
						[% END %]
					</th>
					<th class="value-tipo-consegna-class" style="text-align: center">
						[% IF partyAtTest == 'Non presente' %]
						<input type="radio" checked>
						[% ELSE %]
						<input type="radio">
						[% END %]
					</th>
				</tr>

			</tbody>
		</table>
		
		<table class="dic-responsabilita-class new-page-class">
			<tbody>
				<tr>
					<th class="value-dic-responsabilita-class">Con la presente comunicazione dichiariamo sotto la nostra esclusiva responsabilit&agrave; che l'impianto realizzato ed i materiali installati
sono conformi alle norme/specfiche tecniche di riferimento emesse da Telecom Italia.</th>
				</tr>
			</tbody>
		</table>
		
		<!-- elenco persone accesso -->
		
		<table class="elenco-persone-accesso-class new-page-class">
			<thead>
				<tr>
					<th class="title-elenco-persone-accesso-class">ELENCO DELLE PERSONE PER LE QUALI SI COMUNICA L'ACCESSO:</th>
				</tr>
				<tr>
					<th class="th-box-elenco-persone-accesso-class">
						<table class="tb-elenco-persone-accesso-class">
							<thead>
								<tr>
									<th class="thead-elenco-persone-accesso-class">Cognome</th>
									<th class="thead-elenco-persone-accesso-class">Nome</th>
									<th class="thead-elenco-persone-accesso-class">Qualifica</th>
									<th class="thead-elenco-persone-accesso-class">N&deg; badge di T.I.</th>
									<th class="thead-elenco-persone-accesso-class">N&deg; tess. di ricon.</th>
									<th class="thead-elenco-persone-accesso-class">Note&sup2;</th>
								</tr>
							</thead>
							<tbody>
								<!-- ciclo generazione materiali -->
								[% FOREACH persona IN people %]
								<tr>
									<th class="value-elenco-persone-accesso-class">[% persona.surname %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.name %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.businessRole %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.badge %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.companyCard %]</th>
									<th class="value-elenco-persone-accesso-class">[% persona.note %]</th>
								</tr>
								[% END %]
							</tbody>
						</table>
					</th>					
				</tr>
			</tbody>
		</table>
		
		<!-- elenco persone accesso -->
		
		<!-- rappresentante -->
		<table class="rappresentante-class new-page-class">
			<tbody>
				<tr>
					<th>Nominativo del responsabile del Servizio Prevenzione e protezione aziendale dell'Impresa: [% safetyAccountableName %] [% safetyAccountableSurname %]</th>
				</tr>
				<tr>
					<th><span style="text-decoration: underline;">Nota:</span> il suddetto personale si impegna a firmare il registro delle visite ed a rispettare le "Norme di comportamento Telecom Italia
SpA sugli accessi", le "Norme sulla sicurezza del servizio telefonico", ad operare sempre nel rispetto della legislazione vigente, dei
contratti, Capitolati e norme Tecniche emesse da Telecom Italia SpA.</th>
				</tr>
			</tbody>
		</table>
		<!-- rappresentante -->
		
		<!-- blocco word -->
		<table class="doc-install-allegata-class new-page-class">
			<tbody>
				<tr>
					<th colspan="2" class="title-doc-install-allegata-class">
						DOCUMENTAZIONE D'INSTALLAZIONE E D'IMPIANTO ALLEGATA
					</th>
				</tr>
				<tr>
					<th class="value-doc-install-allegata-class">
						[% IF tdta0001 == '1' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-doc-install-allegata-class">Dichiarazione di conformit&agrave; DLgs 37/2008</span>
					</th>
					<th class="value-doc-install-allegata-class">
						[% IF tdta0004 == '1' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-doc-install-allegata-class">Materiale di scorta</span>
					</th>
				</tr>
				<tr>
					<th class="value-doc-install-allegata-class">
						[% IF tdta0002 == '1' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-doc-install-allegata-class">Rapportazione lavori/Riepilogo prestazioni</span>
					</th>
					<th class="value-doc-install-allegata-class">
						[% IF tdta0005 == '1' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-doc-install-allegata-class">Documentazione tecnica/manuali apparati</span>
					</th>
				</tr>
				<tr>
					<th class="value-doc-install-allegata-class">
						[% IF tdta0003 == '1' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-doc-install-allegata-class">Formulario identificazione rifiuti</span>
					</th>
					<th class="value-doc-install-allegata-class">
						<input type="checkbox" ><span class="text-doc-install-allegata-class">&nbsp;</span>
					</th>
				</tr>
			</tbody>
		</table>
		
		<table class="controlli-collaudi-class new-page-class">
			<tbody>
				<tr>
					<th colspan="3" class="title-controlli-collaudi-class">
						CONTROLLI E COLLAUDI
					</th>
				</tr>
				<tr>
					<th colspan="3" class="title2-controlli-collaudi-class">
						Norma/e di collaudo: <span class="value-tipo-consegna-class">[% tdta0006 %]</span>
					</th>
				<tr>
				<tr>
					<th colspan="3" class="title3-controlli-collaudi-class">Documentazione di collaudo allegata (riportare la data di collaudo e/o il codice di ogni documento)</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						[% IF tdta0007 != '' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-controlli-collaudi-class">Verbale di collaudo "Stand alone"</span>
					</th>
					<th class="value-top-center-controlli-collaudi-class" >:</th>
					<th class="value-top-right-controlli-collaudi-class">[% tdta0007 %]
						&nbsp;
					</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						[% IF tdta0008 != '' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-controlli-collaudi-class">Verbali e/o Modelli di collaudo specifici</span><sup class="sup-value-class">3</sup>
					</th>
					<th class="value-middle-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">[% tdta0008 %]</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						[% IF tdta0009 != '' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-controlli-collaudi-class">Rapporto prova (per i Sistemi di Alimentazione e CDZ)</span>
					</th>
					<th class="value-middle-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">[% tdta0009 %]</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						[% IF tdta0010 != '' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-controlli-collaudi-class">Autocertificazione di collaudo</span><sup class="sup-value-class">4</sup>
					</th>
					<th class="value-middle-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">[% tdta0010 %]</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						[% IF tdta0011 != '' %]
						<input type="checkbox" checked>
						[% ELSE %]
						<input type="checkbox">
						[% END %]
						<span class="text-controlli-collaudi-class">Aggiornamento Banche dati</span>
					</th>
					<th class="value-middle-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">[% tdta0011 %]</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						<input type="checkbox" ><span class="text-controlli-collaudi-class">&nbsp;</span>
					</th>
					<th class="value-middle-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">&nbsp;</th>
				</tr>
				<tr>
					<th class="value-top-left-controlli-collaudi-class">
						<input type="checkbox" ><span class="text-controlli-collaudi-class">&nbsp;</span>
					</th>
					<th class="value-bottom-center-controlli-collaudi-class">:</th>
					<th class="value-top-right-controlli-collaudi-class">&nbsp;</th>
				</tr>
			</tbody>
		</table>		
		<!-- blocco word -->
		
		<!-- totale -->
		
		<table class="totale-cui-class new-page-class">
			<tbody>
				<tr>
					<th colspan="2" class="label-totale-cui-class" >Totale M.O.I. : <span class="value-totale-cui-class">[% moiValue %]</span></th>
				</tr>
				<tr>
					<th colspan="2" class="label-totale-cui-class">Totale forniture : <span class="value-totale-cui-class">[% fornitureValue %]</span></th>
				</tr>
				<tr>
					<th colspan="2" class="label-totale-cui-class">% Avanzamento tecnico : <span class="value-totale-cui-class">[% advancementPercent %]</span></th>
				</tr>
				<tr>
					<th class="label-totale-cui-class" style="width: 50%">Data fine lavori : <span class="value-totale-cui-class">[% endDate %]</span></th>
					<th class="label-totale-cui-class" style="width: 50%;text-align: center">Il Fornitore <span class="value-totale-cui-class">________________________________</span></th>
				</tr>
			</tbody>
		</table>
		
		<!-- a cura funz committente -->
		<table class="totale-cui-class new-page-class">
			<tbody>
				<tr>
					<th rowspan="2" style="width: 30%;text-align: left;font-weight: bold; vertical-align: top">
						Verbale di collaudo da "Sistema di Gestione"
					</th>
					<th style="width: 10%;text-align: left;font-weight: bold; vertical-align: top"><input type="checkbox" > SI</th>
					<th style="width: 60%;text-align: left;font-weight: bold; vertical-align: top">Data di emissione _________________________</th>
				</tr>
				<tr>
					<th style="width: 10%;text-align: left;font-weight: bold; vertical-align: top"><input type="checkbox" > NO </th>
					<th style="width: 60%;text-align: left;font-weight: bold; vertical-align: top"><input type="checkbox" > Non previsto </th>
				</tr>
				<tr>
					<th rowspan="2" style="width: 30%;text-align: left;font-weight: bold; vertical-align: top">
						Per validazione del progetto
					</th>
					<th colspan="2" style="width: 70%;text-align: left;font-weight: bold; vertical-align: top">Data _________________________</th>
				</tr>
				<tr>
					<th colspan="2" style="width: 70%;text-align: left;font-weight: bold; vertical-align: top">Il responsabile Funzione Committente _________________________</th>
				</tr>
			</tbody>
		</table>

		<!-- legenda -->
		<table class="rappresentante-class new-page-class">
			<tbody>
				<tr>
					<th><sup class="sup-value-class">1</sup> La revisione &egrave; quella del progetto esecutivo</th>
				</tr>
				<tr>
					<th><sup class="sup-value-class">2</sup> Indicare eventuale Responsabile cantiere/Preposto (626/94)</th>
				</tr>
				<tr>
					<th><sup class="sup-value-class">3</sup> Definiti da Procedure, Norme e/o I.L. specifiche per tipologia d'impianto.</th>
				</tr>
				<tr>
					<th><sup class="sup-value-class">4</sup> Nel caso di collaudo da parte dell'impresa senza la presenza del personale Telecom.</th>
				</tr>
			</tbody>
		</table>
		<!-- legenda -->		

	</body>
</html>