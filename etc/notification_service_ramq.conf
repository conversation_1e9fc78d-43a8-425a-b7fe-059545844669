
{
	ID_TAG				:	"WPSOCORE_MQ_Consumer_Activity_NotificationService",

	SESSION_TYPE		:	"TARGET",

	SESSION_DESCRIPTION	:	"WPSOCORE::MQ::Consumer::Activity Notification Service Consumer",

	CLASS				:	"WPSOCORE::MQ::Consumer::Activity::NotificationService",

	SOURCE_CONTEXT		:	"${ARTID}",
	TARGET_CONTEXT		:	"API::ART::Act::N",

	DB : {
		ART : {
			ID : "${ARTID}",
			USER : "${ART_SCRIPT_USER}",
			PASSWORD : "${ART_SCRIPT_PASSWORD}",
			DEBUG : "${ART_DB_DEBUG}",
			AUTOSAVE : 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",

	WORK_CONTEXT        : {
		SENDER: "W+ Service Orchestrator <<EMAIL>>",
		ART_APPLICATION_NAME : "${ART_APPLICATION_NAME}",
		DATE_FORMAT : "${NLS_DATE_FORMAT}",
		TIMESTAMP_TZ_FORMAT : "${NLS_TIMESTAMP_TZ_FORMAT}",
		QUEUE_SOURCE_CONTEXT : "${ARTID}",
		QUEUE_TARGET_CONTEXT :	"API::ART::Act::N"
	}
}


