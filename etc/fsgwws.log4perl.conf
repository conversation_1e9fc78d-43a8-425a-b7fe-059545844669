
# log4perl.logger.WebService.WS.FSGWWS.Requests.Incoming = DEBUG, RequestsIncomingLog, RequestsIncomingFullLog
# log4perl.logger.WebService.WS.FSGWWS.Responses.Incoming = DEBUG, ResponsesIncomingLog, ResponsesIncomingFullLog
log4perl.logger.WebService.WS.FSGWWS.Requests.Incoming = DEBUG, RequestsIncomingLog
log4perl.logger.WebService.WS.FSGWWS.Responses.Incoming = DEBUG, ResponsesIncomingLog
log4perl.logger.WebService.WS.FSGWWS.Requests.Outcoming = DEBUG, RequestsOutcomingLog
log4perl.logger.WebService.WS.FSGWWS.Responses.Outcoming = DEBUG, ResponsesOutcomingLog

log4perl.appender.RequestsIncomingLog = Log::Dispatch::File
log4perl.appender.RequestsIncomingLog.Threshold = INFO
log4perl.appender.RequestsIncomingLog.filename =  sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Requests_Incoming.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.RequestsIncomingLog.mode = append
log4perl.appender.RequestsIncomingLog.layout = Log::Log4perl::Layout::JSON
log4perl.appender.RequestsIncomingLog.layout.field.message = %m{chomp}
log4perl.appender.RequestsIncomingLog.layout.field.date = %d
log4perl.appender.RequestsIncomingLog.layout.field.priority = %p
log4perl.appender.RequestsIncomingLog.layout.max_json_length_kb = 1000
log4perl.appender.RequestsIncomingLog.warp_message = 0

# log4perl.appender.RequestsIncomingFullLog = Log::Dispatch::File
# log4perl.appender.RequestsIncomingFullLog.Threshold = DEBUG
# log4perl.appender.RequestsIncomingFullLog.filename = sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Requests_Incoming.full.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) ) }
# log4perl.appender.RequestsIncomingFullLog.mode = append
# log4perl.appender.RequestsIncomingFullLog.layout = Log::Log4perl::Layout::JSON
# log4perl.appender.RequestsIncomingFullLog.layout.field.message = %m{chomp}
# log4perl.appender.RequestsIncomingFullLog.layout.field.date = %d
# log4perl.appender.RequestsIncomingFullLog.layout.field.priority = %p
# log4perl.appender.RequestsIncomingFullLog.layout.max_json_length_kb = 1000
# log4perl.appender.RequestsIncomingFullLog.warp_message = 0

log4perl.appender.ResponsesIncomingLog = Log::Dispatch::File
log4perl.appender.ResponsesIncomingLog.Threshold = INFO
log4perl.appender.ResponsesIncomingLog.filename =  sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Responses_Incoming.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.ResponsesIncomingLog.mode = append
log4perl.appender.ResponsesIncomingLog.layout = Log::Log4perl::Layout::JSON
log4perl.appender.ResponsesIncomingLog.layout.field.message = %m{chomp}
log4perl.appender.ResponsesIncomingLog.layout.field.date = %d
log4perl.appender.ResponsesIncomingLog.layout.field.priority = %p
log4perl.appender.ResponsesIncomingLog.layout.max_json_length_kb = 1000
log4perl.appender.ResponsesIncomingLog.warp_message = 0

# log4perl.appender.ResponsesIncomingFullLog = Log::Dispatch::File
# log4perl.appender.ResponsesIncomingFullLog.Threshold = DEBUG
# log4perl.appender.ResponsesIncomingFullLog.filename = sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Responses_Incoming.full.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) ) }
# log4perl.appender.ResponsesIncomingFullLog.mode = append
# log4perl.appender.ResponsesIncomingFullLog.layout = Log::Log4perl::Layout::JSON
# log4perl.appender.ResponsesIncomingFullLog.layout.field.message = %m{chomp}
# log4perl.appender.ResponsesIncomingFullLog.layout.field.date = %d
# log4perl.appender.ResponsesIncomingFullLog.layout.field.priority = %p
# log4perl.appender.ResponsesIncomingFullLog.layout.max_json_length_kb = 1000
# log4perl.appender.ResponsesIncomingFullLog.warp_message = 0

log4perl.appender.RequestsOutcomingLog = Log::Dispatch::File
log4perl.appender.RequestsOutcomingLog.Threshold = INFO
log4perl.appender.RequestsOutcomingLog.filename =  sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Requests_Outcoming.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.RequestsOutcomingLog.mode = append
log4perl.appender.RequestsOutcomingLog.layout = Log::Log4perl::Layout::JSON
log4perl.appender.RequestsOutcomingLog.layout.field.message = %m{chomp}
log4perl.appender.RequestsOutcomingLog.layout.field.date = %d
log4perl.appender.RequestsOutcomingLog.layout.field.priority = %p
log4perl.appender.RequestsOutcomingLog.layout.max_json_length_kb = 1000
log4perl.appender.RequestsOutcomingLog.warp_message = 0

log4perl.appender.ResponsesOutcomingLog = Log::Dispatch::File
log4perl.appender.ResponsesOutcomingLog.Threshold = INFO
log4perl.appender.ResponsesOutcomingLog.filename =  sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{APACHE_FSGWWS_LOG_PATH} . '/' . 'WebService_WS_FSGWWS_Responses_Outcoming.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.ResponsesOutcomingLog.mode = append
log4perl.appender.ResponsesOutcomingLog.layout = Log::Log4perl::Layout::JSON
log4perl.appender.ResponsesOutcomingLog.layout.field.message = %m{chomp}
log4perl.appender.ResponsesOutcomingLog.layout.field.date = %d
log4perl.appender.ResponsesOutcomingLog.layout.field.priority = %p
log4perl.appender.ResponsesOutcomingLog.layout.max_json_length_kb = 1000
log4perl.appender.ResponsesOutcomingLog.warp_message = 0
