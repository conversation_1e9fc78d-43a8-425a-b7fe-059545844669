
{
	ID_TAG				:	"RepositoryNotice",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Manage notices for API::ART::Repository",
	CLASS				:	"WPSOCORE::MQ::Consumer::RepositoryNotice",
	SOURCE_CONTEXT		:	"REPOSITORY",
	TARGET_CONTEXT		:	"REPOSITORY",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",
	WORK_CONTEXT		: {}
}
