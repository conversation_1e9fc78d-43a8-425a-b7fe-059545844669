
{
	ID_TAG				:	"NetworkPublicPermitsNotifyOpen",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network Public Permits Notify - Apertura",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkPublicPermitsNotifyOpen",
	SOURCE_SERVICE		:	"ENFTTH_AP",
	SOURCE_CONTEXT		:	"PERMESSO_LAVORI",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"PERMESSO_LAVORI",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
