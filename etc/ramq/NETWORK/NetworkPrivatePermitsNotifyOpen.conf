
{
	ID_TAG				:	"NetworkPrivatePermitsNotifyOpen",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network Private Permits Notify - Apertura",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkPrivatePermitsNotifyOpen",
	SOURCE_SERVICE		:	"ENFTTH_AP",
	SOURCE_CONTEXT		:	"PERMESSO_BUILD",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"PERMESSO_BUILD",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
