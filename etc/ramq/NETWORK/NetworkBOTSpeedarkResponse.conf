
{
	ID_TAG				:	"NetworkBOTSpeedarkResponse",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Gestore Response BOT Speedark",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkBOTSpeedarkResponse",
	SOURCE_SERVICE		:	"BOT",
	SOURCE_CONTEXT		:	"SPEEDARK",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"NETWORK",
	RA_DBLINK			:	"",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
