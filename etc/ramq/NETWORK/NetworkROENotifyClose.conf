
{
	ID_TAG				:	"NetworkROENotifyClose",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network ROE Notify - Chiusura",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkROENotifyClose",
	SOURCE_CONTEXT		:	"ROE",
	TARGET_CONTEXT		:	"NETWORK",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
