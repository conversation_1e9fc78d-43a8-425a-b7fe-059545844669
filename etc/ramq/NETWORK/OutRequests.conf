
{
	ID_TAG				:	"NetworkOutRequests",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network - Gestione CIL, CUI e RCI",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::OutRequests",
	SOURCE_SERVICE		:	"ENFTTH_CORE",
	SOURCE_CONTEXT		:	"NETWORK",
	TARGET_SERVICE		:	"NETWORKGW",
	TARGET_CONTEXT		:	"NETWORK",
	RA_DBLINK			:	"",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
