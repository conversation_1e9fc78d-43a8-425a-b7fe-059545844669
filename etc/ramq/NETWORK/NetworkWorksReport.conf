
{
	ID_TAG				:	"NetworkWorksReport",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network - Gestione Works Report",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkWorksReport",
	SOURCE_SERVICE		:	"NETWORKGW",
	SOURCE_CONTEXT		:	"NETWORK",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"NETWORK",
	RA_DBLINK			:	"",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
