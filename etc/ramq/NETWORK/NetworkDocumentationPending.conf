
{
	ID_TAG				:	"NetworkDocumentationPending",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Network Documentation Pending 24h",
	CLASS				:	"WPSOCORE::MQ::Consumer::NETWORK::NetworkDocumentationPending",
	SOURCE_CONTEXT		:	"NETWORK",
	TARGET_CONTEXT		:	"NETWORK",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {
		NETWORK_BASE_URL : "${WPSOUI_HOMEPAGE}/#/OC02/TIM/FTTH/NETWORKS"
	}
}
