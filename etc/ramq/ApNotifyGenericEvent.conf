
{
	ID_TAG				:	"ApNotifyGenericEvent",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Comunicazioni TT001",
	CLASS				:	"WPSOCORE::MQ::Consumer::ApNotifyGenericEvent",
	SOURCE_SERVICE		:	"ENFTTH_AP",
	SOURCE_CONTEXT		:	"GENERIC",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"GENERIC",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
