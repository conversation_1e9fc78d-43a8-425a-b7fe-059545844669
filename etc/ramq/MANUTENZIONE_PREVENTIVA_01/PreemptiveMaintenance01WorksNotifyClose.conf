
{
	ID_TAG				:	"PreemptiveMaintenance01WorksNotifyClose",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"PreemptiveMaintenance01 Works Notify- Chiusura",
	CLASS				:	"WPSOCORE::MQ::Consumer::MANUTENZIONE_PREVENTIVA_01::PreemptiveMaintenance01WorksNotifyClose",
	SOURCE_SERVICE		:	"ENFTTH_WORKS",
	SOURCE_CONTEXT		:	"LAVORI",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"LAVORI",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
