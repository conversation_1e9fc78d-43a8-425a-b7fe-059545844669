
{
	ID_TAG				:	"LC02B<PERSON><PERSON><PERSON><PERSON>",
	SESSION_TYPE		:	"SOURCE",
	SESSION_DESCRIPTION	:	"Gestore ack booking",
	CLASS				:	"WPSOCORE::MQ::Consumer::LC02::BookingACK",
	SOURCE_SERVICE		:	"ENFTTH_CORE",
	SOURCE_CONTEXT		:	"BOOKING",
	TARGET : {
		ENFTTH_CORE	:	"BOOKING",
		ENFTTH_AP:		"BOOKING"
	},
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	USE_ACK_TYPE : "ALL",
	WORK_CONTEXT		: {}
}
