
{
	ID_TAG				:	"ROEPrivatePermitsNotifyClose",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"ROE Private Permits Notify - Chiusura",
	CLASS				:	"WPSOCORE::MQ::Consumer::ROE::ROEPrivatePermitsNotifyClose",
	SOURCE_SERVICE		:	"ENFTTH_AP",
	SOURCE_CONTEXT		:	"PERMESSO_BUILD",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"PERMESSO_BUILD",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
