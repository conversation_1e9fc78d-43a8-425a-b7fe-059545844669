
{
	ID_TAG				:	"ExtraordinaryMaintenanceWorksNotifyOpen",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"ExtraordinaryMaintenance Works Notify- Apertura",
	CLASS				:	"WPSOCORE::MQ::Consumer::MANUTENZIONE_STRAORDINARIA::ExtraordinaryMaintenanceWorksNotifyOpen",
	SOURCE_SERVICE		:	"ENFTTH_WORKS",
	SOURCE_CONTEXT		:	"LAVORI",
	TARGET_SERVICE		:	"ENFTTH_CORE",
	TARGET_CONTEXT		:	"LAVORI",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT		: {}
}
