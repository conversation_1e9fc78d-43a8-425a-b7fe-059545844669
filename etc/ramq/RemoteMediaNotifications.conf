
{
	ID_TAG				:	"RemoteMediaNotifications",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"RemoteMediaNotifications",
	CLASS				:	"WPSOCORE::MQ::Consumer::RemoteMediaNotifications",
	SOURCE_CONTEXT		:	"FIELD_SRV_REQ",
	TARGET_CONTEXT		:	"FIELD_SRV_REQ",
	
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",
	WORK_CONTEXT		: {}
}
